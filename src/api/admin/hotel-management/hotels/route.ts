import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import {
  PostAdminCreateHotel,
  PostAdminDeleteHotel,
  PostAdminUpdateHotel,
} from "./validators";
import { CreateHotelWorkflow } from "src/workflows/hotel-management/hotel/create-hotel";
import { UpdateHotelWorkflow } from "src/workflows/hotel-management/hotel/update-hotel";
import { DeleteHotelWorkflow } from "src/workflows/hotel-management/hotel/delete-hotel";
import { ContainerRegistrationKeys, Modules } from "@camped-ai/framework/utils";
import { RBAC_MODULE } from "../../../../modules/rbac";
import RbacModuleService from "../../../../modules/rbac/service";
import { UserRole } from "../../../../modules/rbac/types";

type PostAdminCreateHotelType = z.infer<typeof PostAdminCreateHotel>;
type PostAdminDeleteHotelType = z.infer<typeof PostAdminDeleteHotel>;
type PostAdminUpdateHotelType = z.infer<typeof PostAdminUpdateHotel>;

export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminCreateHotelType>,
  res: MedusaResponse
) => {
  try {
    // Check permission to create hotels
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    if (req.auth_context?.actor_id) {
      const user = await userService.retrieveUser(req.auth_context.actor_id);
      const userWithRole = {
        ...user,
        created_at: user.created_at.toISOString(),
        updated_at: user.updated_at.toISOString(),
      };
      const hasPermission = await rbacService.hasPermission(
        userWithRole,
        "hotel_management:create" as any
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required_permission: "hotel_management:create",
        });
      }
    } else {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { result } = await CreateHotelWorkflow(req.scope).run({
      //@ts-ignore
      input: req.body,
    });
    res.json({ hotel: result });
  } catch (error) {
    console.error("Error creating hotel:", error);
    res.status(500).json({ error: "Failed to create hotel" });
  }
};

export const PUT = async (
  req: AuthenticatedMedusaRequest<PostAdminUpdateHotelType>,
  res: MedusaResponse
) => {
  try {
    // Check permission to edit hotels
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    if (req.auth_context?.actor_id) {
      const user = await userService.retrieveUser(req.auth_context.actor_id);
      const userWithRole = {
        ...user,
        created_at: user.created_at.toISOString(),
        updated_at: user.updated_at.toISOString(),
      };
      const hasPermission = await rbacService.hasPermission(
        userWithRole,
        "hotel_management:edit" as any
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required_permission: "hotel_management:edit",
        });
      }
    } else {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { result } = await UpdateHotelWorkflow(req.scope).run({
      //@ts-ignore
      input: req.body,
    });
    res.json({ hotel: result });
  } catch (error) {
    console.error("Error updating hotel:", error);
    res.status(500).json({ error: "Failed to update hotel" });
  }
};
export const DELETE = async (
  req: AuthenticatedMedusaRequest<PostAdminDeleteHotelType>,
  res: MedusaResponse
) => {
  try {
    // Check permission to delete hotels
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    if (req.auth_context?.actor_id) {
      const user = await userService.retrieveUser(req.auth_context.actor_id);
      const userWithRole = {
        ...user,
        created_at: user.created_at.toISOString(),
        updated_at: user.updated_at.toISOString(),
      };
      const hasPermission = await rbacService.hasPermission(
        userWithRole,
        "hotel_management:delete" as any
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required_permission: "hotel_management:delete",
        });
      }
    } else {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { result } = await DeleteHotelWorkflow(req.scope).run({
      //@ts-ignore
      input: req.body,
    });

    res.json({ hotel: result });
  } catch (error) {
    console.error("Error deleting hotel:", error);
    res.status(500).json({ error: "Failed to delete hotel" });
  }
};

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    // Check permission to view hotels
    let userWithRole = null;
    if (req.auth_context?.actor_id) {
      try {
        userWithRole = await userService.retrieveUser(
          req.auth_context.actor_id
        );

        // Check if user has permission to view hotels
        const hasPermission = await rbacService.hasPermission(
          userWithRole,
          "hotel_management:view" as any
        );
        if (!hasPermission) {
          return res.status(403).json({
            error: "Insufficient permissions",
            required_permission: "hotel_management:view",
          });
        }
      } catch (error) {
        console.warn("Could not load user RBAC data:", error);
        return res.status(403).json({ error: "Authentication required" });
      }
    }

    const {
      limit = 20,
      offset = 0,
      is_featured,
      star_rating,
      destination_id,
      search,
      searchParams
    } = req.query || {};
    const filters: Record<string, any> = {};

    if (is_featured !== undefined) {
      filters.is_featured = is_featured;
    }

    // Add destination filter if provided
    if (destination_id) {
      filters.destination_id = destination_id;
    }

    // Parse searchParams for additional filters
    const params = searchParams && typeof searchParams === "string"
      ? JSON.parse(searchParams)
      : null;

    if (params?.destination_id) {
      filters.destination_id = params.destination_id;
    }

    // Handle star rating filtering
    let allHotels: any[];
    let totalCount: number;
    let take: number;
    let skip: number;

    // Normalize star_rating to array
    const starRatingArray = star_rating
      ? Array.isArray(star_rating)
        ? star_rating
        : [star_rating]
      : [];

    console.log("Star rating filter received:", star_rating);
    console.log("Normalized star rating array:", starRatingArray);

    // Handle text search at database level
    let searchDestinationIds: string[] = [];
    if (search && typeof search === "string") {
      const searchTerm = search.trim();

      // First, find destinations that match the search term
      try {
        const { data: matchingDestinations } = await query.graph({
          entity: "destination",
          filters: { name: { $ilike: `%${searchTerm}%` } },
          fields: ["id"],
        });

        if (matchingDestinations && matchingDestinations.length > 0) {
          searchDestinationIds = matchingDestinations.map((dest: any) => dest.id);
        }
      } catch (error) {
        console.error("Error searching destinations:", error);
      }

      // Add text search filters to the database query
      const searchFilters: any[] = [
        { name: { $ilike: `%${searchTerm}%` } },
        { description: { $ilike: `%${searchTerm}%` } },
        { address: { $ilike: `%${searchTerm}%` } },
        { location: { $ilike: `%${searchTerm}%` } },
      ];

      // Add destination-based search if we found matching destinations
      if (searchDestinationIds.length > 0) {
        searchFilters.push({ destination_id: { $in: searchDestinationIds } });
      }

      filters.$or = searchFilters;
    }

    // Add star rating filter to database query
    if (starRatingArray.length > 0) {
      const starRatingNumbers = starRatingArray.map(Number).filter((n: number) => !isNaN(n));

      // Create database-level star rating filters
      // For star rating filtering: exact ranges (3 = 3.0-3.9, 4 = 4.0-4.9, etc.)
      const ratingFilters: any[] = [];

      starRatingNumbers.forEach((selectedStar: number) => {
        // Only use 'rating' field since that's the correct field name in the hotel model
        // Filter for exact star rating range: 3 = 3.0 to 3.9, 4 = 4.0 to 4.9, etc.
        ratingFilters.push({
          rating: {
            $gte: selectedStar,
            $lt: selectedStar + 1
          }
        });
      });

      // Combine with existing filters using $or for star rating options
      if (ratingFilters.length > 0) {
        if (filters.$or) {
          // If we already have $or filters (from search), we need to combine them properly
          filters.$and = [
            { $or: filters.$or }, // Existing search filters
            { $or: ratingFilters } // Star rating filters
          ];
          delete filters.$or;
        } else {
          filters.$or = ratingFilters;
        }
      }
    }

    // Execute single database query with all filters applied
    const {
      data: queryHotels,
      metadata: { count: queryTotalCount, take: queryTake, skip: querySkip },
    } = await query.graph({
      entity: "hotel",
      fields: ["*", "roomConfigs.id", "roomConfigs.name", "images.*"],
      filters,
      pagination: {
        skip: Number(offset),
        take: Number(limit),
      },
    });

    allHotels = queryHotels;
    totalCount = queryTotalCount;
    take = queryTake;
    skip = querySkip;

    // Filter hotels based on user permissions
    let filteredHotels = allHotels;
    let filteredCount = totalCount;

    if (userWithRole) {
      const rbacData = userWithRole.metadata?.rbac as any;

      // All users with valid roles can see all hotels
      // Access control is now permission-based, not hotel-based
      if (rbacData?.role !== UserRole.ADMIN) {
        // Non-admin users see all hotels but with permission-based operations
        filteredHotels = allHotels;
        filteredCount = allHotels.length;
      }
    }

    // Fetch destination names for all hotels
    const destinationIds = [...new Set(filteredHotels.map((hotel: any) => hotel.destination_id).filter(Boolean))];
    let destinationMap = new Map<string, string>();

    if (destinationIds.length > 0) {
      try {
        const { data: destinations } = await query.graph({
          entity: "destination",
          filters: { id: { $in: destinationIds } },
          fields: ["id", "name"],
        });

        if (destinations && destinations.length > 0) {
          destinations.forEach((destination: { id: string; name: string }) => {
            destinationMap.set(destination.id, destination.name);
          });
        }
      } catch (error) {
        console.error("Error fetching destination names:", error);
      }
    }

    // Add destination names to hotels
    const hotelsWithDestinations = filteredHotels.map((hotel: any) => ({
      ...hotel,
      destination_name: hotel.destination_id ? destinationMap.get(hotel.destination_id) : null,
    }));

    // Ensure we always return valid data structure, even for empty results
    const response = {
      hotels: hotelsWithDestinations || [],
      count: filteredCount || 0,
      limit: take || Number(limit),
      offset: skip || Number(offset),
    };

    res.json(response);
  } catch (error) {
    console.error("Error in hotel listing:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to list hotels",
    });
  }
};
