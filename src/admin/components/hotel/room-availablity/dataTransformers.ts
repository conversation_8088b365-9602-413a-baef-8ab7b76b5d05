import {
  Room,
  Booking,
  RoomAvailability,
  HotelAvailabilityAPIResponse,
  RoomInventoryStatus,
} from "../types";

/**
 * Transform API room availability data into booking objects for the timeline UI
 */
export function transformAvailabilityToBookings(
  availability: RoomAvailability[]
): Booking[] {
  return availability
    .filter((item) => item.status !== "available") // Only non-available periods
    .map((item, index) => ({
      id: `${item.room_id}-${item.from_date}-${item.to_date}-${item.status}-${index}`,
      room_id: item.room_id,
      guestName: extractGuestNameFromNotes(item.notes, item.order_id),
      checkIn: new Date(item.from_date),
      checkOut: new Date(item.to_date),
      status: mapAPIStatusToEnum(item.status),
      notes: item.notes,
      order_id: item.order_id,
      confirmationNumber: item.order_id ? item.order_id.slice(-8) : undefined,
    }));
}

/**
 * Extract guest name from notes or order ID
 */
function extractGuestNameFromNotes(notes: string, orderId: string): string {
  // Try to extract guest name from notes (only for actual bookings)
  const guestMatch = notes.match(
    /(?:Booking confirmed:|Cart created.*?:)\s*([^|]+)/
  );
  if (guestMatch) {
    return guestMatch[1].trim();
  }

  // If maintenance/cleaning, return appropriate service name
  if (notes.includes("repair") || notes.includes("maintenance")) {
    return "Maintenance";
  }
  if (notes.includes("cleaning")) {
    return "Cleaning";
  }
  if (notes.includes("On Demand")) {
    return "On Request";
  }

  // For actual bookings with order ID, show guest name
  if (orderId) {
    return `Guest ${orderId.slice(-4)}`;
  }

  // For other statuses, return empty string (will be handled by UI)
  return "";
}

/**
 * Map API status strings to RoomInventoryStatus enum
 */
function mapAPIStatusToEnum(status: string): RoomInventoryStatus {
  switch (status.toLowerCase()) {
    case "available":
      return RoomInventoryStatus.AVAILABLE;
    case "booked":
      return RoomInventoryStatus.BOOKED;
    case "reserved":
      return RoomInventoryStatus.RESERVED;
    case "maintenance":
      return RoomInventoryStatus.MAINTENANCE;
    case "cleaning":
      return RoomInventoryStatus.CLEANING;
    case "unavailable":
      return RoomInventoryStatus.UNAVAILABLE;
    case "reserved_unassigned":
      return RoomInventoryStatus.RESERVED_UNASSIGNED;
    case "cart_reserved":
      return RoomInventoryStatus.CART_RESERVED;
    case "on_demand":
      return RoomInventoryStatus.ON_DEMAND;
    default:
      return RoomInventoryStatus.AVAILABLE;
  }
}

/**
 * Get room types from room configurations
 */
export function extractRoomTypes(
  apiResponse: HotelAvailabilityAPIResponse
): string[] {
  const types = new Set<string>();

  apiResponse.room_configs.forEach((config) => {
    types.add(config.title);
  });

  return Array.from(types);
}

/**
 * Get capacity information from room config metadata
 */
export function getRoomCapacity(room: Room, roomConfigs: any[]): number {
  const config = roomConfigs.find((c) => c.id === room.room_config_id);
  return config?.metadata?.max_occupancy || 2;
}

/**
 * Remove duplicate rooms based on room ID
 */
function deduplicateRooms(rooms: Room[]): Room[] {
  const seen = new Set<string>();
  return rooms.filter((room) => {
    if (seen.has(room.id)) {
      console.warn(`Duplicate room found and removed: ${room.id}`);
      return false;
    }
    seen.add(room.id);
    return true;
  });
}

/**
 * Transform complete API response for UI consumption
 */
export function transformAPIResponseForUI(
  apiResponse: HotelAvailabilityAPIResponse
) {
  const bookings = transformAvailabilityToBookings(apiResponse.availability);
  const roomTypes = extractRoomTypes(apiResponse);

  // Deduplicate rooms to prevent duplicate keys
  const deduplicatedRooms = deduplicateRooms(apiResponse.rooms);

  return {
    rooms: deduplicatedRooms,
    roomConfigs: apiResponse.room_configs,
    bookings,
    availability: apiResponse.availability,
    roomTypes,
  };
}

/**
 * Group rooms by configuration type
 */
export function groupRoomsByType(rooms: Room[]): Record<string, Room[]> {
  return rooms.reduce((acc, room) => {
    const type = room.config_name;
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(room);
    return acc;
  }, {} as Record<string, Room[]>);
}
