import { useState, useMemo, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { mockAPIResponse } from "../mockData";
import { transformAPIResponseForUI } from "../dataTransformers";
import { generateDateSlots } from "../timeUtils";
import { format } from "date-fns";
import { cacheSettings } from "../../../../lib/react-query-config";
import { RoomInventoryStatus } from "../../types/booking";

export const useTimelineData = (hotelId?: string) => {
  // UI State
  const [selectedRoomTypes, setSelectedRoomTypes] = useState<string[]>([]);
  const [selectedStatuses, setSelectedStatuses] = useState<
    RoomInventoryStatus[]
  >([]);
  const [hoveredRoomId, setHoveredRoomId] = useState<string | null>(null);

  // Date State - Simple initialization
  const [startDate, setStartDate] = useState<Date>(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today;
  });

  const [endDate, setEndDate] = useState<Date>(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const defaultEndDate = new Date(today);
    defaultEndDate.setDate(defaultEndDate.getDate() + 13);
    return defaultEndDate;
  });

  const queryClient = useQueryClient();

  // API fetch function for TanStack Query
  const fetchHotelAvailability = async (
    fetchStartDate?: Date,
    fetchEndDate?: Date
  ) => {
    const timestamp = new Date().toISOString();

    if (!hotelId) {
      console.log(`[${timestamp}] ❌ FETCH FAILED: No hotel ID provided`);
      throw new Error("Hotel ID is required");
    }

    const formattedStartDate = format(
      fetchStartDate || startDate,
      "yyyy-MM-dd"
    );
    const formattedEndDate = format(fetchEndDate || endDate, "yyyy-MM-dd");

    const url = `/admin/hotel-management/availability?hotel_id=${hotelId}&start_date=${formattedStartDate}&end_date=${formattedEndDate}&consolidate=true`;

    console.log(`[${timestamp}] 📤 FETCHING HOTEL AVAILABILITY:`, {
      hotelId,
      startDate: formattedStartDate,
      endDate: formattedEndDate,
      url,
    });

    const response = await fetch(url);

    console.log(`[${timestamp}] 📥 FETCH RESPONSE:`, {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      headers: Object.fromEntries(response.headers.entries()),
    });

    if (!response.ok) {
      console.log(`[${timestamp}] ❌ FETCH FAILED:`, {
        status: response.status,
        statusText: response.statusText,
      });
      throw new Error(`Failed to fetch availability data: ${response.status}`);
    }

    const apiData = await response.json();
    console.log(`[${timestamp}] ✅ FETCH SUCCESS:`, {
      availabilityCount: apiData.availability?.length || 0,
      bookingsCount: apiData.bookings?.length || 0,
      roomsCount: apiData.rooms?.length || 0,
      roomTypesCount: apiData.room_types?.length || 0,
      dataKeys: Object.keys(apiData),
    });

    return apiData;
  };

  // TanStack Query for hotel availability data
  const {
    data = mockAPIResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: [
      "hotel-availability",
      hotelId,
      format(startDate, "yyyy-MM-dd"),
      format(endDate, "yyyy-MM-dd"),
    ],
    queryFn: () => fetchHotelAvailability(),
    enabled: !!hotelId, // Only run query when hotelId is available
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // Use optimized cache settings for real-time availability data
    ...cacheSettings.realTime,
    // Don't refetch on window focus for availability data to reduce server load
    refetchOnWindowFocus: false,
    refetchOnReconnect: true, // Do refetch on reconnect for fresh data
  });

  // Debug log when query data changes
  useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 📊 QUERY DATA CHANGED:`, {
      hotelId,
      isLoading,
      hasError: !!error,
      errorMessage: error?.message,
      hasData: !!data,
      dataKeys: data ? Object.keys(data) : [],
      availabilityCount: data?.availability?.length || 0,
      bookingsCount: data?.bookings?.length || 0,
      roomsCount: data?.rooms?.length || 0,
      queryKey: [
        "hotel-availability",
        hotelId,
        format(startDate, "yyyy-MM-dd"),
        format(endDate, "yyyy-MM-dd"),
      ],
    });
  }, [data, isLoading, error, hotelId, startDate, endDate]);

  // Manual refresh function for Apply button
  const refreshData = async (newStartDate: Date, newEndDate: Date) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 🔄 REFRESH DATA TRIGGERED:`, {
      hotelId,
      currentStartDate: format(startDate, "yyyy-MM-dd"),
      currentEndDate: format(endDate, "yyyy-MM-dd"),
      newStartDate: format(newStartDate, "yyyy-MM-dd"),
      newEndDate: format(newEndDate, "yyyy-MM-dd"),
      queryKey: ["hotel-availability", hotelId],
    });

    // Update the state first
    setStartDate(newStartDate);
    setEndDate(newEndDate);

    console.log(`[${timestamp}] 📝 DATE STATE UPDATED`);

    // Invalidate existing queries for this hotel to ensure fresh data
    console.log(`[${timestamp}] 🔄 INVALIDATING QUERIES:`, {
      queryKey: ["hotel-availability", hotelId],
    });

    await queryClient.invalidateQueries({
      queryKey: ["hotel-availability", hotelId],
    });

    console.log(
      `[${timestamp}] ✅ QUERIES INVALIDATED - REFETCH WILL TRIGGER AUTOMATICALLY`
    );

    // The useQuery will automatically refetch with the new dates due to the queryKey dependency
    // No need to manually fetch since the queryKey includes the dates and will trigger a refetch
  };

  // Simple data transformation - just transform once
  const transformedData = useMemo(() => {
    return transformAPIResponseForUI(data);
  }, [data]);

  const { rooms, bookings, roomTypes } = transformedData;

  const dateSlots = useMemo(
    () => generateDateSlots(startDate, endDate),
    [startDate, endDate]
  );

  // Helper function to get all statuses for a room
  const getRoomStatuses = (roomId: string): RoomInventoryStatus[] => {
    // Find all bookings for this room within the current date range
    const roomBookings = bookings.filter(
      (booking) => booking.room_id === roomId
    );

    if (roomBookings.length === 0) {
      return [RoomInventoryStatus.AVAILABLE];
    }

    // Get all unique statuses for this room
    const roomStatuses = [
      ...new Set(roomBookings.map((booking) => booking.status)),
    ] as RoomInventoryStatus[];

    console.log(`Room ${roomId} has statuses:`, roomStatuses);
    return roomStatuses;
  };

  // Helper function to check if a room has any of the selected statuses
  const roomHasAnySelectedStatus = (
    roomId: string,
    selectedStatuses: RoomInventoryStatus[]
  ): boolean => {
    const roomStatuses = getRoomStatuses(roomId);
    const hasMatch = roomStatuses.some((status) =>
      selectedStatuses.includes(status)
    );
    console.log(
      `Room ${roomId} statuses:`,
      roomStatuses,
      "selected:",
      selectedStatuses,
      "match:",
      hasMatch
    );
    return hasMatch;
  };

  // Helper function to check if a room has available time periods within the date range
  const roomHasAvailableTimeInRange = (roomId: string): boolean => {
    const roomBookings = bookings.filter(
      (booking) => booking.room_id === roomId
    );

    // If no bookings, room is completely available
    if (roomBookings.length === 0) {
      return true;
    }

    // Create array of all dates in the range
    const allDates: Date[] = [];
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      allDates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Check each date to see if it's covered by a booking
    for (const date of allDates) {
      const isDateBooked = roomBookings.some((booking) => {
        const bookingStart = new Date(booking.checkIn);
        const bookingEnd = new Date(booking.checkOut);
        bookingStart.setHours(0, 0, 0, 0);
        bookingEnd.setHours(23, 59, 59, 999);
        date.setHours(12, 0, 0, 0); // Set to noon to avoid timezone issues

        return date >= bookingStart && date <= bookingEnd;
      });

      // If we find any date that's not booked, the room has available time
      if (!isDateBooked) {
        return true;
      }
    }

    // All dates are booked
    return false;
  };

  // Helper function to get available date slots for a specific room
  const getAvailableDateSlots = (roomId: string) => {
    const roomBookings = bookings.filter(
      (booking) => booking.room_id === roomId
    );

    // Create set of unavailable dates
    const unavailableDates = new Set<string>();

    roomBookings.forEach((booking) => {
      const start = new Date(booking.checkIn);
      const end = new Date(booking.checkOut);

      // Normalize dates to avoid timezone issues
      start.setHours(0, 0, 0, 0);
      end.setHours(0, 0, 0, 0);

      const current = new Date(start);

      // Include all dates from check-in to check-out (inclusive of check-in, exclusive of check-out)
      while (current < end) {
        unavailableDates.add(current.toISOString().split("T")[0]); // YYYY-MM-DD format
        current.setDate(current.getDate() + 1);
      }
    });

    console.log(
      `Room ${roomId} unavailable dates:`,
      Array.from(unavailableDates)
    );

    // Filter dateSlots to only include available dates
    const availableSlots = dateSlots
      .filter((date) => {
        const dateStr = date.toISOString().split("T")[0];
        const isAvailable = !unavailableDates.has(dateStr);
        console.log(
          `Date ${dateStr}: ${isAvailable ? "AVAILABLE" : "UNAVAILABLE"}`
        );
        return isAvailable;
      })
      .map((date) => ({
        date,
        roomId,
        status: RoomInventoryStatus.AVAILABLE,
      }));

    console.log(`Room ${roomId} available slots:`, availableSlots.length);
    return availableSlots;
  };

  const filteredRooms = useMemo(() => {
    let filtered = rooms;

    // Filter by room types
    if (selectedRoomTypes.length > 0) {
      filtered = filtered.filter((room) =>
        selectedRoomTypes.includes(room.config_name)
      );
    }

    // Filter by room statuses
    if (selectedStatuses.length > 0) {
      filtered = filtered.filter((room) => {
        // Check if room has any of the selected statuses

        // Special handling for available status
        if (selectedStatuses.includes(RoomInventoryStatus.AVAILABLE)) {
          // Check if room has available time periods in the date range
          const hasAvailableTimeInRange = roomHasAvailableTimeInRange(room.id);

          // If only "Available" is selected, show rooms with available time periods
          if (
            selectedStatuses.length === 1 &&
            selectedStatuses[0] === RoomInventoryStatus.AVAILABLE
          ) {
            return hasAvailableTimeInRange;
          }

          // If "Available" is selected along with other statuses
          const hasOtherSelectedStatus = roomHasAnySelectedStatus(
            room.id,
            selectedStatuses.filter((s) => s !== RoomInventoryStatus.AVAILABLE)
          );
          return hasAvailableTimeInRange || hasOtherSelectedStatus;
        }

        // For non-available statuses, check if room has any of the selected statuses
        return roomHasAnySelectedStatus(room.id, selectedStatuses);
      });
    }

    return filtered;
  }, [selectedRoomTypes, selectedStatuses, rooms, bookings]);

  const groupedRooms = useMemo(
    () =>
      filteredRooms.reduce((acc, room) => {
        if (!acc[room.config_name]) {
          acc[room.config_name] = [];
        }
        acc[room.config_name].push(room);
        return acc;
      }, {} as Record<string, any[]>),
    [filteredRooms]
  );

  const weekBookings = useMemo(() => {
    let filteredBookings = bookings.filter((booking) => {
      const bookingDate = new Date(booking.checkIn);
      bookingDate.setHours(0, 0, 0, 0);
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      return bookingDate >= start && bookingDate <= end;
    });

    // If status filter is active, also filter booking blocks by selected statuses
    if (selectedStatuses.length > 0) {
      // Special handling for "Available" status
      if (selectedStatuses.includes(RoomInventoryStatus.AVAILABLE)) {
        // If only "Available" is selected, don't show any booking blocks
        // (we want to see the green available background only)
        if (
          selectedStatuses.length === 1 &&
          selectedStatuses[0] === RoomInventoryStatus.AVAILABLE
        ) {
          filteredBookings = []; // Remove all booking blocks to show available periods
        } else {
          // If "Available" + other statuses are selected, show non-available booking blocks
          filteredBookings = filteredBookings.filter(
            (booking) =>
              selectedStatuses.includes(booking.status) &&
              booking.status !== RoomInventoryStatus.AVAILABLE
          );
        }
      } else {
        // For non-available statuses, filter normally
        filteredBookings = filteredBookings.filter((booking) =>
          selectedStatuses.includes(booking.status)
        );
      }
    }

    return filteredBookings;
  }, [startDate, endDate, bookings, selectedStatuses]);

  // Generate available blocks when "Available" status is selected
  const availableBlocks = useMemo(() => {
    if (!selectedStatuses.includes(RoomInventoryStatus.AVAILABLE)) {
      return [];
    }

    const blocks: Array<{
      id: string;
      room_id: string;
      date: Date;
      status: RoomInventoryStatus;
    }> = [];

    filteredRooms.forEach((room) => {
      const availableSlots = getAvailableDateSlots(room.id);
      availableSlots.forEach((slot, index) => {
        blocks.push({
          id: `available-${room.id}-${slot.date.toISOString()}-${index}`,
          room_id: room.id,
          date: slot.date,
          status: RoomInventoryStatus.AVAILABLE,
        });
      });
    });

    return blocks;
  }, [filteredRooms, selectedStatuses, startDate, endDate, bookings]);

  return {
    selectedRoomTypes,
    setSelectedRoomTypes,
    selectedStatuses,
    setSelectedStatuses,
    hoveredRoomId,
    setHoveredRoomId,

    startDate,
    setStartDate,
    endDate,
    setEndDate,
    dateSlots,
    filteredRooms,
    groupedRooms,
    weekBookings,
    availableBlocks,
    roomTypes,
    rooms, // Add all rooms for the filter component
    TIMELINE_WIDTH: dateSlots.length * 150 + 20, // Dynamic width based on actual date range + padding

    // API state
    isLoading,
    error: error?.message || null,
    hasRealData: data !== mockAPIResponse,
    rawApiData: data, // Provide raw API data for unallocated bookings processing

    // Manual refresh function
    refreshData,
  };
};
