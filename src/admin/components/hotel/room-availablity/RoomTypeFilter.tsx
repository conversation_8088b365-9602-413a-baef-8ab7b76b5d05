import React from "react";
import { MultiSelect } from "../../common/MultiSelect";

interface RoomTypeFilterProps {
  selectedRoomTypes: string[];
  onRoomTypesChange: (types: string[]) => void;
  roomTypes: string[];
}

const RoomTypeFilter: React.FC<RoomTypeFilterProps> = ({
  selectedRoomTypes,
  onRoomTypesChange,
  roomTypes,
}) => {
  const options = roomTypes
    .filter((type) => type !== "All Types") // Remove "All Types" from options since we handle it with empty selection
    .map((type) => ({
      value: type,
      label: type,
    }));

  const handleChange = (values: string[]) => {
    onRoomTypesChange(values);
  };

  return (
    <div className="flex flex-col">
      <label className="text-xs text-muted-foreground mb-1">Room Types</label>
      <MultiSelect
        options={options}
        selectedValues={selectedRoomTypes}
        onChange={handleChange}
        placeholder="All Room Types"
        className="min-w-[240px]"
        showSelectAll={true}
        showSelectedTags={false}
      />
    </div>
  );
};

export default RoomTypeFilter;
