import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Text } from "@camped-ai/ui";
import { MapPin, Settings } from "lucide-react";
import { Rating } from "@mui/material";
import OptimizedImage from "../shared/OptimizedImage";
import ImageSkeleton from "../shared/ImageSkeleton";

interface Hotel {
  id: string;
  name: string;
  destination_name?: string;
  location?: string;
  description?: string;
  image_url?: string;
  star?: number;
  is_internal?: boolean;
  is_featured?: boolean;
  status?: string;
}

interface HotelCardProps {
  hotel: Hotel;
  onClick: () => void;
  priority?: boolean;
}

const HotelCard: React.FC<HotelCardProps> = ({
  hotel,
  onClick,
  priority = false,
}) => {
  const getStatusBadge = (status?: string) => {
    if (!status) return null;

    const statusConfig = {
      active: { label: "Active", color: "green" as const },
      inactive: { label: "Inactive", color: "grey" as const },
      featured: { label: "Featured", color: "purple" as const },
    };

    const config =
      statusConfig[status.toLowerCase() as keyof typeof statusConfig];
    if (!config) return null;

    return (
      <Badge color={config.color} className="text-xs">
        {config.label}
      </Badge>
    );
  };

  // Show loading skeleton if there's an image URL, otherwise show gradient
  const customPlaceholder = hotel.image_url ? (
    <ImageSkeleton className="absolute inset-0" variant="card" />
  ) : (
    <div className="absolute inset-0 bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500" />
  );

  console.log({hotel})
 
  return (
    <div
      className="bg-card rounded-lg border shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer group"
      onClick={onClick}
    >
      {/* Image Container */}
      <div className="h-48 bg-muted relative overflow-hidden rounded-t-lg">
        <OptimizedImage
          src={hotel.image_url}
          alt={hotel.name}
          className="w-full h-full"
          placeholder={customPlaceholder}
          priority={priority}
        />

        {/* Left side badges */}
        <div className="absolute top-3 left-3 flex flex-wrap gap-2">
          {hotel.is_featured && (
            <Badge color="purple" className="text-xs">
              Featured
            </Badge>
          )}
          {hotel.is_internal && (
            <Badge color="grey" className="text-xs">
              Internal
            </Badge>
          )}
          {getStatusBadge(hotel.status)}
        </div>

        {/* Right side rating */}
        <div className="absolute top-3 right-3">
          {hotel.star && hotel.star > 0 ? (
            <div className="flex items-center gap-1 bg-black/60 backdrop-blur-sm rounded-md px-2 py-1">
              <Rating
                value={hotel.star}
                readOnly
                size="small"
                sx={{
                  "& .MuiRating-iconFilled": {
                    color: "#facc15", // yellow-400
                  },
                  "& .MuiRating-iconEmpty": {
                    color: "#d1d5db", // gray-300
                  },
                }}
              />
              <span className="text-xs text-white font-medium">
                {hotel.star}
              </span>
            </div>
          ) : (
            <div className="bg-black/60 backdrop-blur-sm rounded-md px-2 py-1">
              <Text className="text-xs text-white">No Rating</Text>
            </div>
          )}
        </div>

        {/* Hover overlay */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
      </div>

      {/* Content */}
      <div className="p-4 space-y-3">
        {/* Hotel Name */}
        <div>
          <h3 className="font-semibold text-lg text-foreground group-hover:text-primary transition-colors duration-200 line-clamp-1">
            {hotel.name}
          </h3>
        </div>

        {/* Description */}
        {hotel.description && (
          <div className="text-sm text-muted-foreground line-clamp-2">
            {hotel.description}
          </div>
        )}

        {/* Location and Destination */}
        <div className="text-sm text-muted-foreground space-y-1">
          {hotel.location && (
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 flex-shrink-0" />
              <span className="line-clamp-1">{hotel.location}</span>
            </div>
          )}
          {hotel.destination_name && (
            <div className="flex items-center gap-2">
              <span className="w-4 h-4 flex-shrink-0 text-center">📍</span>
              <span className="line-clamp-1">{hotel.destination_name}</span>
            </div>
          )}
        </div>

        {/* Action Button */}
        <Button
          variant="secondary"
          className="w-full"
          onClick={(e) => {
            e.stopPropagation();
            onClick();
          }}
        >
          <Settings className="w-4 h-4 mr-2" />
          Manage
        </Button>
      </div>
    </div>
  );
};

export default HotelCard;
