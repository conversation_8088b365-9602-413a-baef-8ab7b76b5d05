import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "react-router-dom";
import { useMemo, useCallback, useEffect } from "react";

// Types
export interface HotelWithDestination {
  id: string;
  name: string;
  handle: string;
  description?: string;
  is_active: boolean;
  is_featured: boolean;
  destination_id?: string;
  destination_name?: string;
  rating?: number;
  website?: string;
  email?: string;
  phone?: string;
  address?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  image_url?: string;
  star?: number;
  images?: Array<{
    id: string;
    url: string;
    metadata?: { isThumbnail?: boolean };
  }>;
}

export interface HotelsManagementResponse {
  hotels: HotelWithDestination[];
  count: number;
  limit: number;
  offset: number;
}

export interface HotelManagementFilters {
  limit?: number;
  offset?: number;
  page?: number;
  is_featured?: boolean | null;
  is_active?: boolean | null;
  search?: string;
  destination_id?: string | null;
  star_rating?: number[] | null;
}

// Query Keys
export const hotelManagementKeys = {
  all: ["hotels-management"] as const,
  lists: () => [...hotelManagementKeys.all, "list"] as const,
  list: (filters: HotelManagementFilters) =>
    [...hotelManagementKeys.lists(), filters] as const,
};

// Hook for hotels management with URL synchronization
export const useHotelsManagement = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  // Set default URL parameters only on initial load
  useEffect(() => {
    const hasPageParam = searchParams.has("page");
    const hasLimitParam = searchParams.has("limit");

    // Only set defaults if both page and limit are missing (initial load)
    if (!hasPageParam && !hasLimitParam) {
      const defaultParams = new URLSearchParams();
      defaultParams.set("page", "1");
      defaultParams.set("limit", "10");
      setSearchParams(defaultParams, { replace: true });
    }
  }, []); // Empty dependency array to run only once on mount

  // Parse URL parameters
  const urlFilters = useMemo(() => {
    const limit = parseInt(searchParams.get("limit") || "10");
    const page = parseInt(searchParams.get("page") || "1");
    const offset = (page - 1) * limit;
    const search = searchParams.get("search") || "";
    const is_featured = searchParams.get("is_featured");
    const is_active = searchParams.get("is_active");
    const destination_id = searchParams.get("destination_id");
    const star_rating = searchParams.get("star_rating");

    return {
      limit,
      offset,
      page,
      search: search || undefined,
      is_featured:
        is_featured === "true" ? true : is_featured === "false" ? false : null,
      is_active:
        is_active === "true" ? true : is_active === "false" ? false : null,
      destination_id: destination_id || null,
      star_rating: star_rating ? star_rating.split(",").map(Number) : null,
    };
  }, [searchParams]);

  // Update URL parameters
  const updateFilters = useCallback(
    (newFilters: Partial<HotelManagementFilters>) => {
      const newSearchParams = new URLSearchParams(searchParams);

      // Handle pagination
      if (newFilters.limit !== undefined) {
        newSearchParams.set("limit", newFilters.limit.toString());
      }
      if (newFilters.page !== undefined) {
        newSearchParams.set("page", newFilters.page.toString());
      }

      // Handle search
      if (newFilters.search !== undefined) {
        if (newFilters.search.trim()) {
          newSearchParams.set("search", newFilters.search);
        } else {
          newSearchParams.delete("search");
        }
        // Reset to page 1 when search changes
        newSearchParams.set("page", "1");
      }

      // Handle filters
      if (newFilters.is_featured !== undefined) {
        if (newFilters.is_featured !== null) {
          newSearchParams.set("is_featured", newFilters.is_featured.toString());
        } else {
          newSearchParams.delete("is_featured");
        }
        // Reset to page 1 when filters change
        newSearchParams.set("page", "1");
      }

      if (newFilters.is_active !== undefined) {
        if (newFilters.is_active !== null) {
          newSearchParams.set("is_active", newFilters.is_active.toString());
        } else {
          newSearchParams.delete("is_active");
        }
        // Reset to page 1 when filters change
        newSearchParams.set("page", "1");
      }

      if (newFilters.destination_id !== undefined) {
        if (newFilters.destination_id) {
          newSearchParams.set("destination_id", newFilters.destination_id);
        } else {
          newSearchParams.delete("destination_id");
        }
        // Reset to page 1 when filters change
        newSearchParams.set("page", "1");
      }

      if (newFilters.star_rating !== undefined) {
        if (newFilters.star_rating && newFilters.star_rating.length > 0) {
          newSearchParams.set("star_rating", newFilters.star_rating.join(","));
        } else {
          newSearchParams.delete("star_rating");
        }
        // Reset to page 1 when filters change
        newSearchParams.set("page", "1");
      }

      setSearchParams(newSearchParams);
    },
    [searchParams, setSearchParams]
  );

  // Fetch hotels data
  const { data, isLoading, error, refetch, isFetching } = useQuery({
    queryKey: hotelManagementKeys.list(urlFilters),
    queryFn: async (): Promise<HotelsManagementResponse> => {
      const params = new URLSearchParams();

      if (urlFilters.limit) params.append("limit", urlFilters.limit.toString());
      if (urlFilters.offset) params.append("offset", urlFilters.offset.toString());

      if (urlFilters.is_featured !== null && urlFilters.is_featured !== undefined) {
        params.append("is_featured", urlFilters.is_featured.toString());
      }
      if (urlFilters.is_active !== null && urlFilters.is_active !== undefined) {
        params.append("is_active", urlFilters.is_active.toString());
      }
      if (urlFilters.destination_id) {
        params.append("destination_id", urlFilters.destination_id);
      }
      if (urlFilters.star_rating && urlFilters.star_rating.length > 0) {
        params.append("star_rating", urlFilters.star_rating.join(","));
      }
      if (urlFilters.search) params.append("search", urlFilters.search);

      const url = `/admin/hotel-management/hotels${
        params.toString() ? `?${params.toString()}` : ""
      }`;

      const response = await fetch(url, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch hotels");
      }

      const result = await response.json();

      // Process hotels to extract image URLs and star ratings
      const processedHotels = (result.hotels || []).map((hotel: any) => {
        // Get the first image URL if available
        let imageUrl: string | undefined = undefined;
        if (hotel.images && hotel.images.length > 0) {
          const thumbnailImage = hotel.images.find(
            (img: any) => img.metadata?.isThumbnail
          );
          const selectedImage = thumbnailImage || hotel.images[0];
          if (selectedImage?.url && selectedImage.url.trim() !== "") {
            imageUrl = selectedImage.url;
          }
        }

        // Extract star rating
        const starRating =
          hotel.rating || hotel.star || hotel.star_rating || 0;

        return {
          ...hotel,
          image_url: imageUrl,
          star: starRating,
        };
      });

      return {
        hotels: processedHotels,
        count: result.count || 0,
        limit: result.limit || urlFilters.limit,
        offset: result.offset || urlFilters.offset,
      };
    },
    staleTime: 1000 * 60 * 5, // 5 minutes cache
    refetchOnWindowFocus: false,
  });

  // Pagination calculations
  const pagination = useMemo(() => {
    const totalCount = data?.count || 0;
    const currentPage = urlFilters.page;
    const pageSize = urlFilters.limit;
    const totalPages = Math.ceil(totalCount / pageSize);
    const canPreviousPage = currentPage > 1;
    const canNextPage = currentPage < totalPages;

    return {
      currentPage,
      pageSize,
      totalPages,
      totalCount,
      canPreviousPage,
      canNextPage,
    };
  }, [data?.count, urlFilters.page, urlFilters.limit]);

  // Pagination functions
  const nextPage = useCallback(() => {
    if (pagination.canNextPage) {
      updateFilters({ page: pagination.currentPage + 1 });
    }
  }, [pagination.canNextPage, pagination.currentPage, updateFilters]);

  const previousPage = useCallback(() => {
    if (pagination.canPreviousPage) {
      updateFilters({ page: pagination.currentPage - 1 });
    }
  }, [pagination.canPreviousPage, pagination.currentPage, updateFilters]);

  const goToPage = useCallback(
    (page: number) => {
      updateFilters({ page });
    },
    [updateFilters]
  );

  const changePageSize = useCallback(
    (newPageSize: number) => {
      updateFilters({ limit: newPageSize, page: 1 });
    },
    [updateFilters]
  );

  // Filter functions
  const updateSearch = useCallback(
    (search: string) => {
      updateFilters({ search });
    },
    [updateFilters]
  );

  const updateFeaturedFilter = useCallback(
    (is_featured: boolean | null) => {
      updateFilters({ is_featured });
    },
    [updateFilters]
  );

  const updateActiveFilter = useCallback(
    (is_active: boolean | null) => {
      updateFilters({ is_active });
    },
    [updateFilters]
  );

  const updateDestinationFilter = useCallback(
    (destination_id: string | null) => {
      updateFilters({ destination_id });
    },
    [updateFilters]
  );

  const updateStarRatingFilter = useCallback(
    (star_rating: number[] | null) => {
      updateFilters({ star_rating });
    },
    [updateFilters]
  );

  const clearFilters = useCallback(() => {
    updateFilters({
      is_featured: null,
      is_active: null,
      search: "",
      destination_id: null,
      star_rating: null,
      page: 1,
    });
  }, [updateFilters]);

  return {
    // Data
    hotels: data?.hotels || [],
    isLoading,
    isFetching,
    error,
    refetch,

    // Current filters
    filters: urlFilters,

    // Pagination
    pagination,
    nextPage,
    previousPage,
    goToPage,
    changePageSize,

    // Filter functions
    updateSearch,
    updateFeaturedFilter,
    updateActiveFilter,
    updateDestinationFilter,
    updateStarRatingFilter,
    clearFilters,
  };
};
