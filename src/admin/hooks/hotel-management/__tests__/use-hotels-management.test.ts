import { renderHook, act } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useHotelsManagement } from '../use-hotels-management';

// Mock fetch
global.fetch = jest.fn();

const createWrapper = (initialUrl = '/') => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('useHotelsManagement pagination', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
    // Mock successful API response
    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({
        hotels: [],
        count: 0,
        limit: 10,
        offset: 0,
      }),
    });
  });

  it('should not reset page when URL parameters change', async () => {
    const wrapper = createWrapper();
    
    const { result } = renderHook(() => useHotelsManagement(), {
      wrapper,
    });

    // Wait for initial load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Navigate to page 2
    act(() => {
      result.current.goToPage(2);
    });

    // Check that page is set to 2
    expect(result.current.pagination.currentPage).toBe(2);

    // Simulate another URL change (like a filter change)
    act(() => {
      result.current.updateSearch('test');
    });

    // Page should reset to 1 when search changes (this is expected behavior)
    expect(result.current.pagination.currentPage).toBe(1);

    // Navigate to page 2 again
    act(() => {
      result.current.goToPage(2);
    });

    // Page should stay at 2 and not reset automatically
    expect(result.current.pagination.currentPage).toBe(2);
  });

  it('should set default parameters only once on mount', async () => {
    const wrapper = createWrapper();
    
    const { result, rerender } = renderHook(() => useHotelsManagement(), {
      wrapper,
    });

    // Wait for initial load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Check that default parameters are set
    expect(result.current.filters.page).toBe(1);
    expect(result.current.filters.limit).toBe(10);

    // Navigate to page 2
    act(() => {
      result.current.goToPage(2);
    });

    expect(result.current.pagination.currentPage).toBe(2);

    // Rerender the hook (simulating component re-render)
    rerender();

    // Page should still be 2, not reset to 1
    expect(result.current.pagination.currentPage).toBe(2);
  });
});
