import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";
import { cacheSettings } from "../../lib/react-query-config";
import { toast } from "@camped-ai/ui";
import { useDestinationsForHotelManagement } from "../supplier-products-services/use-destinations";
import { useMemo } from "react";

// Types
export interface HotelManagement {
  id: string;
  name: string;
  handle: string;
  description?: string;
  is_active: boolean;
  is_featured: boolean;
  destination_id: string;
  destination_name?: string;
  location?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  star_rating?: number;
  rating?: number;
  star?: number;
  check_in_time?: string;
  check_out_time?: string;
  policies?: Record<string, any>;
  amenities?: string[];
  tags?: string[];
  metadata?: Record<string, any>;
  images?: Array<{
    id: string;
    url: string;
    metadata?: { isThumbnail?: boolean };
  }>;
  image_url?: string;
  is_internal?: boolean;
  is_pets_allowed?: boolean;
  created_at: string;
  updated_at: string;
}

export interface HotelManagementResponse {
  hotels: HotelManagement[];
  count: number;
  limit: number;
  offset: number;
}

export interface HotelManagementFilters {
  limit?: number;
  offset?: number;
  page?: number;
  is_active?: boolean;
  is_featured?: boolean;
  destination_id?: string;
  star_rating?: number[];
  search?: string;
  searchParams?: Record<string, any>;
}

export interface RoomType {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface RoomTypesResponse {
  roomTypes: RoomType[];
}

// Query Keys
export const hotelManagementKeys = {
  all: ["hotel-management"] as const,
  hotels: () => [...hotelManagementKeys.all, "hotels"] as const,
  hotelsList: (filters: HotelManagementFilters) =>
    [...hotelManagementKeys.hotels(), "list", filters] as const,
  hotel: (id: string) =>
    [...hotelManagementKeys.hotels(), "detail", id] as const,
  roomTypes: () => [...hotelManagementKeys.all, "room-types"] as const,
  roomTypesList: () => [...hotelManagementKeys.roomTypes(), "list"] as const,
};

// Hook to fetch hotels with optimized destination resolution
export const useHotelManagement = (filters: HotelManagementFilters = {}) => {
  return useQuery({
    queryKey: hotelManagementKeys.hotelsList(filters),
    queryFn: async (): Promise<HotelManagementResponse> => {
      const params = new URLSearchParams();

      // Add pagination parameters
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());

      // Add filter parameters
      if (filters.is_featured !== undefined) {
        params.append("is_featured", filters.is_featured.toString());
      }
      if (filters.is_active !== undefined) {
        params.append("is_active", filters.is_active.toString());
      }

      // Add star rating filter
      if (filters.star_rating && filters.star_rating.length > 0) {
        console.log("Adding star rating filter to API call:", filters.star_rating);
        filters.star_rating.forEach(rating => {
          params.append("star_rating", rating.toString());
        });
      }

      // Add destination filter
      if (filters.destination_id) {
        params.append("destination_id", filters.destination_id);
      }

      // Add search filter
      if (filters.search) {
        params.append("search", filters.search);
      }

      // Add other search parameters
      if (filters.searchParams) {
        params.append("searchParams", JSON.stringify(filters.searchParams));
      }

      const url = `/admin/hotel-management/hotels${
        params.toString() ? `?${params.toString()}` : ""
      }`;

      const response = (await sdk.client.fetch(url)) as HotelManagementResponse;

      // Process hotels to extract image URLs and star ratings
      if (response.hotels) {
        response.hotels = response.hotels.map((hotel) => {
          // Get the first image URL if available
          let imageUrl: string | undefined = undefined;
          if (hotel.images && hotel.images.length > 0) {
            const thumbnailImage = hotel.images.find(
              (img) => img.metadata?.isThumbnail
            );
            const selectedImage = thumbnailImage || hotel.images[0];
            if (selectedImage?.url && selectedImage.url.trim() !== "") {
              imageUrl = selectedImage.url;
            }
          }

          // Extract star rating
          const starRating =
            hotel.rating || hotel.star || hotel.star_rating || 0;

          return {
            ...hotel,
            image_url: imageUrl,
            star: starRating,
          };
        });
      }

      return response;
    },
    staleTime: cacheSettings.configurations.staleTime, // 15 minutes
    gcTime: cacheSettings.configurations.gcTime, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook to fetch room types
export const useRoomTypes = () => {
  return useQuery({
    queryKey: hotelManagementKeys.roomTypesList(),
    queryFn: async (): Promise<RoomTypesResponse> => {
      return (await sdk.client.fetch("/admin/room-types")) as RoomTypesResponse;
    },
    staleTime: cacheSettings.configurations.staleTime, // 15 minutes
    gcTime: cacheSettings.configurations.gcTime, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    retry: 2,
  });
};

// Hook to fetch a single hotel
export const useHotelManagementDetail = (id: string) => {
  return useQuery({
    queryKey: hotelManagementKeys.hotel(id),
    queryFn: async (): Promise<{ hotel: HotelManagement }> => {
      return (await sdk.client.fetch(
        `/admin/hotel-management/hotels/${id}`
      )) as { hotel: HotelManagement };
    },
    enabled: !!id,
    staleTime: cacheSettings.hotelPricing.staleTime, // 10 minutes
    gcTime: cacheSettings.hotelPricing.gcTime, // 30 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Mutation to create a hotel
export const useCreateHotel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: any) => {
      return (await sdk.client.fetch("/admin/hotel-management/hotels", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })) as any;
    },
    onSuccess: () => {
      // Invalidate and refetch hotel lists
      queryClient.invalidateQueries({
        queryKey: hotelManagementKeys.hotels(),
      });

      toast.success("Success", {
        description: "Hotel created successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error creating hotel:", error);
      toast.error("Error", {
        description: "Failed to create hotel",
      });
    },
  });
};

// Mutation to update a hotel
export const useUpdateHotel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      return (await sdk.client.fetch(`/admin/hotel-management/hotels/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })) as any;
    },
    onSuccess: (_, { id }) => {
      // Invalidate specific hotel and hotel lists
      queryClient.invalidateQueries({
        queryKey: hotelManagementKeys.hotel(id),
      });
      queryClient.invalidateQueries({
        queryKey: hotelManagementKeys.hotels(),
      });

      toast.success("Success", {
        description: "Hotel updated successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error updating hotel:", error);
      toast.error("Error", {
        description: "Failed to update hotel",
      });
    },
  });
};

// Mutation to delete a hotel
export const useDeleteHotel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      return (await sdk.client.fetch(`/admin/hotel-management/hotels/${id}`, {
        method: "DELETE",
      })) as any;
    },
    onSuccess: () => {
      // Invalidate hotel lists
      queryClient.invalidateQueries({
        queryKey: hotelManagementKeys.hotels(),
      });

      toast.success("Success", {
        description: "Hotel deleted successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error deleting hotel:", error);
      toast.error("Error", {
        description: "Failed to delete hotel",
      });
    },
  });
};

// Optimized hook that fetches hotels with destination names included via server-side JOIN
export const useHotelManagementWithDestinations = (
  filters: HotelManagementFilters = {}
) => {
  // Fetch hotels with destination names already included from the API
  const {
    data: hotelsData,
    isLoading: hotelsLoading,
    error: hotelsError,
    refetch: refetchHotels,
  } = useHotelManagement(filters);

  // Fetch destinations separately for filter dropdown (still needed for UI)
  const {
    data: destinationsData,
    isLoading: destinationsLoading,
    error: destinationsError,
  } = useDestinationsForHotelManagement();

  // Hotels already have destination_name from the API, no client-side join needed
  const hotelsWithDestinations = useMemo(() => {
    return hotelsData?.hotels || [];
  }, [hotelsData]);

  // Create destination lookup map for filter UI
  const destinationMap = useMemo(() => {
    if (!destinationsData?.destinations) return {};

    const map: Record<string, string> = {};
    destinationsData.destinations.forEach((dest) => {
      map[dest.id] = dest.name;
    });
    return map;
  }, [destinationsData]);

  return {
    hotels: hotelsWithDestinations,
    hotelsData: hotelsData,
    destinationsData: destinationsData,
    destinationMap,
    isLoading: hotelsLoading || destinationsLoading,
    hotelsLoading,
    destinationsLoading,
    error: hotelsError || destinationsError,
    hotelsError,
    destinationsError,
    refetch: refetchHotels,
    totalCount: hotelsData?.count || 0,
    limit: hotelsData?.limit || 0,
    offset: hotelsData?.offset || 0,
  };
};
