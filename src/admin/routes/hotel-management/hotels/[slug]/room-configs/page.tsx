import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  FocusModal,
  Toaster,
  toast,
  Badge,
  Input,
  usePrompt,
  Tooltip,
  IconButton,
} from "@camped-ai/ui";
import { PlusMini, ChevronLeft } from "@camped-ai/icons";
import {
  Bed,
  Users,
  Home,
  Edit,
  Trash2,
  Search,
  Coffee,
  Eye,
  Upload,
  Download,
  Info,
  Map,
  Building2,
  ArrowLeft,
} from "lucide-react";
import OutlineButton from "../../../../../components/shared/OutlineButton";
import BulkImportModal from "../../../../../components/room-config/bulk-import-modal";
import ExportModal from "../../../../../components/room-config/export-modal";
import "../modal-fix.css"; // Import custom CSS to fix z-index issues with modals
import "../../../../../styles/room-config-modal-fix.css"; // Import custom CSS to fix scrolling issues
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../../hooks/use-rbac";

// Import the simplified form component
import SimpleRoomConfigForm from "../../../../../components/simple-room-config-form";
import LanguageSelector from "../../../../../components/language-selector";

interface RoomConfig {
  id: string;
  name: string;
  type: string;
  description?: string;
  room_size?: string;
  amenities?: string[];
  bed_type?: string;
  max_extra_beds: number;
  max_cots: number;
  max_adults: number;
  max_adults_beyond_capacity: number;
  max_children: number;
  max_infants: number;
  max_occupancy: number;
  hotel_id: string;
  images?: any[];
  thumbnail?: string;
}

interface Hotel {
  id: string;
  name: string;
  description?: string;
  address?: string;
  city?: string;
  country?: string;
  postal_code?: string;
  phone?: string;
  email?: string;
  website?: string;
  destination_id?: string;
  category_id?: string;
  images?: any[];
}

const RoomConfigsPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const prompt = usePrompt();
  const { hasPermission } = useRbac();
  const [hotel, setHotel] = useState<Hotel | null>(null);
  const [roomConfigs, setRoomConfigs] = useState<RoomConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [editId, setEditId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("en");
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);

  useEffect(() => {
    fetchHotelAndRoomConfigs();
  }, [slug]);

  const fetchHotelAndRoomConfigs = async () => {
    setIsLoading(true);
    try {
      // For now, use a mock hotel
      const mockHotel: Hotel = {
        id: slug || "hotel_mock",
        name: "",
        description: "A beautiful hotel in the heart of the city",
        address: "123 Main St",
        city: "New York",
        country: "USA",
        postal_code: "10001",
        phone: "************",
        email: "<EMAIL>",
        website: "https://samplehotel.com",
        destination_id: "destination_1",
        category_id: "category_1",
        images: [],
      };

      setHotel(mockHotel);

      // Fetch room configurations from the API
      try {
        console.log(`Fetching room configurations for hotel: ${mockHotel.id}`);
        // Add a cache-busting parameter to avoid browser caching
        const timestamp = new Date().getTime();
        // Use the direct API endpoint
        const roomConfigsResponse = await fetch(
          `/admin/direct-room-configs?hotel_id=${mockHotel.id}&_=${timestamp}`,
          {
            credentials: "include",
            // Add cache control headers to prevent caching
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
          }
        );

        let roomConfigsData;
        try {
          roomConfigsData = await roomConfigsResponse.json();
        } catch (jsonError) {
          console.error("Error parsing JSON response:", jsonError);
          throw new Error("Failed to parse room configurations response");
        }

        if (
          roomConfigsData.roomConfigs &&
          Array.isArray(roomConfigsData.roomConfigs)
        ) {
          console.log(
            `Found ${roomConfigsData.roomConfigs.length} room configurations`
          );

          const processedRoomConfigs = roomConfigsData.roomConfigs.map(
            (config: any) => {
              const processed = {
                id: config.id || `temp_${Date.now()}`,
                name: config.name || "Unnamed Room Configuration",
                type: config.type || "",
                description: config.description || "",
                room_size: config.room_size || "",
                bed_type: config.bed_type || "",
                max_extra_beds: config.max_extra_beds || 0,
                max_cots: config.max_cots || 0,
                max_adults: config.max_adults || 1,
                max_adults_beyond_capacity:
                  config.max_adults_beyond_capacity || 0,
                max_children: config.max_children || 0,
                max_infants: config.max_infants || 0,
                max_occupancy: config.max_occupancy || 1,
                amenities: Array.isArray(config.amenities)
                  ? config.amenities
                  : [],
                hotel_id: config.hotel_id || mockHotel.id,
                images: [],
                thumbnail: config.thumbnail || "",
              };

              return processed;
            }
          );

          // Fetch images for each room config
          for (const roomConfig of processedRoomConfigs) {
            try {
              const imagesResponse = await fetch(
                `/admin/room-configs/${roomConfig.id}/images`,
                {
                  credentials: "include",
                }
              );

              if (imagesResponse.ok) {
                const imagesData = await imagesResponse.json();
                roomConfig.images = imagesData.images || [];

                // Set thumbnail if available
                const thumbnailImage = roomConfig.images.find(
                  (img: any) => img.isThumbnail
                );
                if (thumbnailImage) {
                  roomConfig.thumbnail = thumbnailImage.url;
                } else if (roomConfig.images.length > 0) {
                  roomConfig.thumbnail = roomConfig.images[0].url;
                }
              }
            } catch (error) {
              console.error(
                `Error fetching images for room config ${roomConfig.id}:`,
                error
              );
            }
          }

          setRoomConfigs(processedRoomConfigs);
        } else {
          console.log("No room configurations found, using empty array");
          // Use empty array if none found from API
          setRoomConfigs([]);
        }
      } catch (error) {
        console.error("Error fetching room configurations:", error);
        toast.error("Error", {
          description: "Failed to fetch room configurations.",
        });
        // Use empty array if error fetching from API
        setRoomConfigs([]);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Error", {
        description: "Failed to load hotel and room configurations",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenModal = (isEdit = false, id: string | null = null) => {
    setEditMode(isEdit);
    setEditId(id);
    setOpen(true);
  };

  const handleDeleteRoomConfig = async (id: string) => {
    const shouldDelete = await prompt({
      title: "Delete Room Configuration",
      description:
        "Are you sure you want to delete this room configuration? This action cannot be undone.",
      confirmText: "Delete",
      cancelText: "Cancel",
    });

    if (!shouldDelete) return;

    try {
      // Delete the room configuration via API
      const response = await fetch(
        `/admin/hotel-management/room-configs/${id}`,
        {
          method: "DELETE",
          credentials: "include",
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Failed to delete room configuration"
        );
      }

      // Remove the deleted room config from the state
      setRoomConfigs(roomConfigs.filter((config) => config.id !== id));

      toast.success("Success", {
        description: "Room configuration deleted successfully",
      });

      // Refresh the list after deletion
      setTimeout(() => {
        fetchHotelAndRoomConfigs();
      }, 500);
    } catch (error) {
      console.error("Error deleting room configuration:", error);
      toast.error("Error", {
        description:
          error instanceof Error
            ? error.message
            : "Failed to delete room configuration",
      });
    }
  };

  const getBadgeColorForRoomType = (
    type: string
  ): "green" | "blue" | "purple" | "orange" | "red" | "grey" => {
    switch (type) {
      case "standard":
        return "blue";
      case "deluxe":
        return "green";
      case "suite":
        return "purple";
      case "family":
        return "orange";
      case "executive":
        return "red";
      default:
        return "grey";
    }
  };

  const filteredRoomConfigs = roomConfigs.filter(
    (config) =>
      config.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      config.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      config.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <div className="flex flex-col min-h-full">
        {/* Back Navigation - Outside Header */}
        <div className="border-b border-gray-200 dark:border-gray-700 py-4 px-8">
          <div className="flex items-center gap-4">
            <IconButton
              onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
            >
              <ArrowLeft className="w-4 h-4" />
            </IconButton>
            <Heading level="h1" className="text-xl text-gray-900 dark:text-gray-100">
              Room Configurations
            </Heading>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 px-8 pb-8">
          <Container className="py-6">
            {/* Header Section */}
            <div className="flex flex-col gap-4 mb-6">

            {/* Search and Actions Row */}
            <div className="flex flex-col lg:flex-row gap-3 lg:items-center lg:justify-between">
              {/* Search */}
              <div className="relative flex-shrink-0 w-full lg:w-80">
                <Input
                  placeholder="Search room configurations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9 h-10 rounded-lg border-gray-300 dark:border-gray-600 w-full"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 dark:text-gray-500" />
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-2 lg:flex-nowrap">
                <Button
                  variant="secondary"
                  onClick={() =>
                    navigate(`/hotel-management/hotels/${slug}/rooms`)
                  }
                  className="flex items-center gap-2 h-10 whitespace-nowrap"
                >
                  <Building2 className="w-4 h-4" />
                  <span className="hidden sm:inline">View All Rooms</span>
                  <span className="sm:hidden">Rooms</span>
                </Button>
                <Button
                  variant="secondary"
                  onClick={() =>
                    navigate(`/hotel-management/hotels/${slug}/floor-plan`)
                  }
                  className="flex items-center gap-2 h-10 whitespace-nowrap"
                >
                  <Map className="w-4 h-4" />
                  <span className="hidden sm:inline">Floor Plan</span>
                  <span className="sm:hidden">Plan</span>
                </Button>
                {hasPermission("rooms:view") && (
                  <OutlineButton
                    onClick={() => setExportModalOpen(true)}
                    className="h-10 rounded-lg shadow-sm flex items-center gap-1 whitespace-nowrap"
                  >
                    <Download className="w-4 h-4" />
                    <span>Export</span>
                  </OutlineButton>
                )}
                {hasPermission("rooms:bulk_import") && (
                  <OutlineButton
                    onClick={() => setImportModalOpen(true)}
                    className="h-10 rounded-lg shadow-sm flex items-center gap-1 whitespace-nowrap"
                  >
                    <Upload className="w-4 h-4" />
                    <span>Import</span>
                  </OutlineButton>
                )}
                {hasPermission("rooms:create") && (
                  <Button
                    variant="primary"
                    onClick={() => handleOpenModal()}
                    className="rounded-lg shadow-sm flex items-center gap-2 h-10 whitespace-nowrap"
                  >
                    <PlusMini />
                    <span className="hidden sm:inline">Add Room Configuration</span>
                    <span className="sm:hidden">Add</span>
                  </Button>
                )}
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="animate-pulse space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-gray-100 h-32 rounded-lg"></div>
              ))}
            </div>
          ) : filteredRoomConfigs.length === 0 ? (
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center">
              <div className="flex flex-col items-center justify-center py-8">
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-full mb-4">
                  {searchQuery ? (
                    <Search className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                  ) : (
                    <Bed className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                  )}
                </div>
                <Heading level="h3" className="text-xl font-semibold mb-2 text-gray-900 dark:text-gray-100">
                  {searchQuery
                    ? "No matching room configurations"
                    : "No room configurations yet"}
                </Heading>
                <Text className="text-gray-500 dark:text-gray-400 mb-6 max-w-md">
                  {searchQuery
                    ? "We couldn't find any room configurations matching your search criteria."
                    : "Start by creating your first room configuration to define the types of rooms available in your hotel."}
                </Text>
                {searchQuery ? (
                  <Button
                    variant="secondary"
                    onClick={() => setSearchQuery("")}
                    className="rounded-lg shadow-sm"
                  >
                    <Search className="w-4 h-4 mr-2" />
                    Clear Search
                  </Button>
                ) : hasPermission("rooms:create") ? (
                  <Button
                    variant="primary"
                    onClick={() => handleOpenModal()}
                    className="rounded-lg shadow-sm bg-gradient-to-r py-2"
                  >
                    <PlusMini />
                    <span>Create Your First Room Configuration</span>
                  </Button>
                ) : null}
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {filteredRoomConfigs.map((roomConfig) => (
                <div
                  key={roomConfig.id}
                  className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md dark:hover:shadow-lg transition-all overflow-hidden group"
                >
                  {/* Room Configuration Card Header */}
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 p-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <div className="bg-white dark:bg-gray-700 p-2 rounded-full shadow-sm">
                          <Bed className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                        </div>
                        <div>
                          <Heading level="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            {roomConfig.name}
                          </Heading>
                        </div>
                      </div>
                      <div className="flex gap-2 opacity-70 group-hover:opacity-100 transition-opacity">
                        {hasPermission("rooms:edit") && (
                          <Button
                            variant="secondary"
                            size="small"
                            onClick={() => handleOpenModal(true, roomConfig.id)}
                            className="h-8 px-2"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        )}
                        {hasPermission("rooms:delete") && (
                          <Button
                            variant="danger"
                            size="small"
                            onClick={() => handleDeleteRoomConfig(roomConfig.id)}
                            className="h-8 px-2"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Room Configuration Card Body */}
                  <div className="p-4">
                    <div className="flex gap-4 mb-4">
                      {/* Thumbnail Image */}
                      <div className="w-24 h-24 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 flex-shrink-0">
                        {roomConfig.thumbnail ? (
                          <img
                            src={roomConfig.thumbnail}
                            alt={roomConfig.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500">
                            <Bed className="w-8 h-8" />
                          </div>
                        )}
                      </div>

                      {/* Description */}
                      <div className="flex-1">
                        <Text className="text-gray-600 dark:text-gray-300 line-clamp-3">
                          {roomConfig.description || "No description provided"}
                        </Text>

                        {/* Image Count Badge */}
                        {roomConfig.images && roomConfig.images.length > 0 && (
                          <div className="mt-2 inline-flex items-center gap-1 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full text-xs text-gray-600 dark:text-gray-300">
                            <Eye className="w-3 h-3" />
                            {roomConfig.images.length}{" "}
                            {roomConfig.images.length === 1
                              ? "image"
                              : "images"}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      {/* Left Column - Room Details */}
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <div className="bg-blue-50 dark:bg-blue-900/30 p-1.5 rounded-md">
                            <Home className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <Text className="text-xs text-gray-500 dark:text-gray-400">
                              Room Size
                            </Text>
                            <Text className="text-sm font-medium dark:text-gray-200">
                              {roomConfig.room_size || "Not specified"}
                            </Text>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <div className="bg-green-50 dark:bg-green-900/30 p-1.5 rounded-md">
                            <Bed className="w-4 h-4 text-green-600 dark:text-green-400" />
                          </div>
                          <div>
                            <Text className="text-xs text-gray-500 dark:text-gray-400">
                              Bed Type
                            </Text>
                            <Text className="text-sm font-medium dark:text-gray-200">
                              {roomConfig.bed_type
                                ? roomConfig.bed_type.charAt(0).toUpperCase() +
                                  roomConfig.bed_type.slice(1)
                                : "Not specified"}
                            </Text>
                          </div>
                        </div>
                      </div>

                      {/* Right Column - Occupancy Details */}
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <div className="bg-purple-50 dark:bg-purple-900/30 p-1.5 rounded-md">
                            <Users className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                          </div>
                          <div>
                            <Text className="text-xs text-gray-500 dark:text-gray-400">
                              Occupancy
                            </Text>
                            <Text className="text-sm font-medium dark:text-gray-200">
                              Max {roomConfig.max_occupancy} guests
                            </Text>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <div className="bg-amber-50 dark:bg-amber-900/30 p-1.5 rounded-md">
                            <Coffee className="w-4 h-4 text-amber-600 dark:text-amber-400" />
                          </div>
                          <div>
                            <Text className="text-xs text-gray-500 dark:text-gray-400">
                              Amenities
                            </Text>
                            {roomConfig.amenities &&
                            roomConfig.amenities.length > 0 ? (
                              roomConfig.amenities.length <= 2 ? (
                                <Text className="text-sm font-medium dark:text-gray-200">
                                  {roomConfig.amenities.join(", ")}
                                </Text>
                              ) : (
                                <Tooltip
                                  content={roomConfig.amenities.join(", ")}
                                >
                                  <div className="flex items-center gap-1 cursor-help">
                                    <Text className="text-sm font-medium dark:text-gray-200">
                                      {`${roomConfig.amenities.length} amenities`}
                                      <span className="text-xs text-gray-500 dark:text-gray-400 block truncate max-w-[150px]">
                                        {roomConfig.amenities
                                          .slice(0, 2)
                                          .join(", ")}
                                        ...
                                      </span>
                                    </Text>
                                    <Info className="w-3 h-3 text-gray-400 dark:text-gray-500" />
                                  </div>
                                </Tooltip>
                              )
                            ) : (
                              <Text className="text-sm font-medium dark:text-gray-200">
                                None listed
                              </Text>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Room Configuration Card Footer */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 p-3 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="flex -space-x-2">
                        <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center text-xs text-blue-700 dark:text-blue-300 font-medium">
                          {roomConfig.max_adults}
                        </div>
                        <div className="w-6 h-6 rounded-full bg-pink-100 dark:bg-pink-900/50 flex items-center justify-center text-xs text-pink-700 dark:text-pink-300 font-medium">
                          {roomConfig.max_children}
                        </div>
                      </div>
                      <Text className="text-xs text-gray-500 dark:text-gray-400">
                        {roomConfig.max_adults} adults,{" "}
                        {roomConfig.max_children} children
                      </Text>
                    </div>
                    {hasPermission("rooms:view") && (
                      <Button
                        variant="secondary"
                        size="small"
                        className="h-8"
                        onClick={() =>
                          navigate(
                            `/hotel-management/hotels/${slug}/room-configs/${roomConfig.id}/rooms`
                          )
                        }
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        View Rooms
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
          </Container>
        </div>
      </div>

      {/* Create/Edit Room Configuration Modal */}
      <FocusModal open={open} onOpenChange={setOpen}>
        <FocusModal.Content className="flex flex-col h-full max-h-[95vh] bg-white dark:bg-gray-900">
          <FocusModal.Header className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center w-full py-4 px-6">
              <Heading level="h2" className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                {editMode ? "Edit Room Type" : "Create New Room Type"}
              </Heading>
              <LanguageSelector
                selectedLanguage={selectedLanguage}
                onLanguageChange={setSelectedLanguage}
              />
            </div>
          </FocusModal.Header>
          <FocusModal.Body className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900">
            <SimpleRoomConfigForm
              hotelId={hotel?.id || ""}
              initialData={
                editMode
                  ? roomConfigs.find((config) => config.id === editId)
                  : undefined
              }
              isEdit={editMode}
              selectedLanguage={selectedLanguage}
              onLoadingChange={setIsFormSubmitting}
              onComplete={(success: boolean) => {
                setOpen(false);
                if (success) {
                  // Add a small delay before fetching to ensure the API has time to process
                  setTimeout(() => {
                    console.log(
                      "Refreshing room configurations after create/update"
                    );
                    console.log(`Using hotel ID: ${hotel?.id}`);
                    fetchHotelAndRoomConfigs();
                  }, 500);
                }
              }}
            />
          </FocusModal.Body>
          <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 p-6 bg-white dark:bg-gray-800">
            <div className="flex justify-end gap-3">
              <Button
                variant="secondary"
                onClick={() => setOpen(false)}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                isLoading={isFormSubmitting}
                disabled={isFormSubmitting}
                onClick={() => {
                  // This is just a placeholder - the actual save functionality is in the form
                  document.getElementById("room-config-save-button")?.click();
                }}
                className="px-6"
              >
                {isFormSubmitting
                  ? "Saving..."
                  : editMode
                  ? "Update Room Type"
                  : "Create Room Type"}
              </Button>
            </div>
          </div>
        </FocusModal.Content>
      </FocusModal>

      {/* Bulk Import Modal */}
      <BulkImportModal
        open={importModalOpen}
        hotelId={slug}
        onClose={() => {
          setImportModalOpen(false);
          // Refresh data when modal closes with a slight delay to ensure server has processed everything
          setTimeout(() => {
            console.log("Refreshing room configurations after import");
            fetchHotelAndRoomConfigs();
          }, 500);
        }}
      />

      {/* Export Modal */}
      <ExportModal
        open={exportModalOpen}
        onClose={() => setExportModalOpen(false)}
        hotelId={hotel?.id}
      />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Room Configurations",
  icon: Bed,
});

export default RoomConfigsPage;
